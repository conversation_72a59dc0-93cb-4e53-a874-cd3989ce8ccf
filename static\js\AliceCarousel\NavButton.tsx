import { IconButton } from '@mui/material';
import classNames from 'classnames';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { ReactComponent as ArrowRight } from '../images/icons/ui-navigation/arrow_right_fill.svg';
import { ReactComponent as ArrowLeft } from '../images/icons/ui-navigation/arrow_left_fill.svg';

type NavButtonProps = {
  classes?: Record<string, string>;
  disabled: boolean;
  next?: boolean;
  prev?: boolean;
};

const useStyles = makeStyles({
  root: {},
  prev: {
    left: 0,
    justifyContent: 'start',
  },
  next: {
    right: 0,
    justifyContent: 'end',
    marginLeft: '-100%',
  },
});

function NavButton(props: NavButtonProps): React.ReactElement {
  const classes = useStyles(props);
  const { disabled, next, prev } = props;
  return (
    <div
      className={classNames(classes.root, {
        [classes.next]: next,
        [classes.prev]: prev,
      })}
    >
      <IconButton disabled={disabled} size="large">
        {next && !prev && <ArrowRight />}
        {prev && !next && <ArrowLeft />}
      </IconButton>
    </div>
  );
}

export default NavButton;
