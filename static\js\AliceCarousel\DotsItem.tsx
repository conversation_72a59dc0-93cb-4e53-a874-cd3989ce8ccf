import { IconButton } from '@mui/material';
import classNames from 'classnames';
import React from 'react';
import makeStyles from '@mui/styles/makeStyles';
import { DotsItem as RenderDotsItemType } from 'react-alice-carousel';

type DotsItemProps = {
  classes?: Record<string, string>;
  active: boolean;
};

const SIZE = 0.6;
const useStyles = makeStyles(
  (theme) => ({
    root: {
      padding: 0,
    },
    icon: {
      width: theme.spacing(SIZE),
      height: theme.spacing(SIZE),
      minWidth: theme.spacing(SIZE),
      minHeight: theme.spacing(SIZE),
      margin: theme.spacing(1.1),
      borderRadius: 999,
      padding: 0,
      backgroundColor: theme.palette.neutral.main,
      '&$active': {
        backgroundColor: theme.palette.primary.main,
      },
    },
    active: {},
  }),
  { name: 'DotsItem' },
);

function DotsItem(props: DotsItemProps): React.ReactElement {
  const { active } = props;
  const classes = useStyles(props);

  return (
    <IconButton size="small" className={classes.root}>
      <span className={classNames(classes.icon, { [classes.active]: active })} />
    </IconButton>
  );
}

export const renderDotsItem = ({ isActive }: RenderDotsItemType): React.ReactElement => (
  <DotsItem active={isActive} />
);

export default DotsItem;
