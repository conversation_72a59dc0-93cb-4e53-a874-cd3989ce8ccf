import { Photo as PhotoType, PhotoSize, PhotoSizeItem } from '../types/WorkoutPayload';

const makeComparator = (
  photoSize: [number, number] | PhotoSize,
): ((psi: PhotoSizeItem) => boolean) => {
  let comparator;
  if (typeof photoSize === 'string') {
    comparator = (size: PhotoSizeItem) => size.size === photoSize;
  } else {
    const [width, height] = photoSize;
    comparator = (size: PhotoSizeItem) => size.width >= width && size.height >= height;
  }
  return comparator;
};

const smallerComparator = (a: PhotoSizeItem, b: PhotoSizeItem) => (a.width > b.width ? 1 : -1);

export const getPhotoUrl = (photo: PhotoType, photoSize: [number, number] | PhotoSize): string => {
  const size = [...(photo.sizes || [])].sort(smallerComparator).find(makeComparator(photoSize));
  return size?.url || photo.url;
};
