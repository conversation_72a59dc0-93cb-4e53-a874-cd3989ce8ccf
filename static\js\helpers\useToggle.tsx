import React from 'react';

const useToggle = (
  initialState = false,
  onValue = true,
  offValue = false,
): [boolean, () => void, () => void] => {
  const [on, setState] = React.useState(initialState);
  const handleOff = React.useCallback(() => setState(offValue), [offValue]);
  const handleOn = React.useCallback(() => setState(onValue), [onValue]);
  return [on, handleOn, handleOff];
};
export default useToggle;
